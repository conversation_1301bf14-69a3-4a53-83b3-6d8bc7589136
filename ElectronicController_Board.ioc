#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=UART5_RX
Dma.Request1=USART2_RX
Dma.Request2=USART1_RX
Dma.Request3=USART3_RX
Dma.RequestsNb=4
Dma.UART5_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.0.Instance=DMA1_Stream0
Dma.UART5_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.0.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.0.Mode=DMA_NORMAL
Dma.UART5_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.0.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.2.Instance=DMA2_Stream2
Dma.USART1_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.2.Mode=DMA_NORMAL
Dma.USART1_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.1.Instance=DMA1_Stream5
Dma.USART2_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.1.Mode=DMA_NORMAL
Dma.USART2_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.3.Instance=DMA1_Stream1
Dma.USART3_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.3.Mode=DMA_NORMAL
Dma.USART3_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.3.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SPI1
Mcu.IP4=SYS
Mcu.IP5=UART5
Mcu.IP6=USART1
Mcu.IP7=USART2
Mcu.IP8=USART3
Mcu.IPNb=9
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PD9
Mcu.Pin11=PA9
Mcu.Pin12=PA10
Mcu.Pin13=PA13
Mcu.Pin14=PA14
Mcu.Pin15=PC12
Mcu.Pin16=PD2
Mcu.Pin17=PD5
Mcu.Pin18=PD6
Mcu.Pin19=VP_SYS_VS_Systick
Mcu.Pin2=PA4
Mcu.Pin3=PA5
Mcu.Pin4=PA6
Mcu.Pin5=PA7
Mcu.Pin6=PC4
Mcu.Pin7=PC5
Mcu.Pin8=PB11
Mcu.Pin9=PD8
Mcu.PinsNb=20
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.13.0
MxDb.Version=DB.6.0.130
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.UART5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.Locked=true
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Locked=true
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB11.Locked=true
PB11.Signal=GPIO_Output
PC12.Locked=true
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.Locked=true
PC5.Signal=GPIO_Output
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD5.Locked=true
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PD8.Locked=true
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Locked=true
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=ElectronicController_Board.ioc
ProjectManager.ProjectName=ElectronicController_Board
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_UART5_Init-UART5-false-HAL-true,5-MX_USART2_UART_Init-USART2-false-HAL-true,6-MX_SPI1_Init-SPI1-false-HAL-true,7-MX_USART1_UART_Init-USART1-false-HAL-true,8-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_8
SPI1.CalculateBaudRate=10.5 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
