Dependencies for Project 'ElectronicController_Board', Target 'ElectronicController_Board': (DO NOT MODIFY !)
CompilerVersion: 5060750::V5.06 update 6 (build 750)::.\ARMCC
F (startup_stm32f407xx.s)(0x6801E03D)(--cpu Cortex-M4.fp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_ElectronicController_Board

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

--pd "__UVISION_VERSION SETA 525" --pd "_RTE_ SETA 1" --pd "STM32F407xx SETA 1"

--list startup_stm32f407xx.lst --xref -o electroniccontroller_board\startup_stm32f407xx.o --depend electroniccontroller_board\startup_stm32f407xx.d)
F (..\Components\interface\src\uart.c)(0x67FB28E6)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O3 -Otime --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../App -I ../Components/ssd1106/inc -I ../Components/ssd1106/interface -I ../Components/interface/inc -I ../Components/interface -I ../Components/ringbuffer -I ../Components/multi_timer -I ../Components/motor -I ..\Components\pid

-I.\RTE\_ElectronicController_Board

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-D__UVISION_VERSION="525" -D_RTE_ -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o electroniccontroller_board\uart.o --omf_browse electroniccontroller_board\uart.crf --depend electroniccontroller_board\uart.d)
I (../Components/interface/inc/uart.h)(0x674C3DEA)
I (../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h)(0x67D552C4)
I (../Core/Inc/stm32f4xx_hal_conf.h)(0x67F5EEBA)
I (../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc.h)(0x67D552C4)
I (../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_def.h)(0x67D552C4)
I (../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f4xx.h)(0x67D552C4)
I (../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f407xx.h)(0x67D552C4)
I (../Drivers/CMSIS/Include/core_cm4.h)(0x67D552AF)
F (../Core/Src/main.c)(0x681300AE)()
F (../Core/Src/gpio.c)(0x67F5EEB3)()
F (../Core/Src/dma.c)(0x6801E03B)()
F (../Core/Src/spi.c)(0x67FA69D3)()
F (../Core/Src/usart.c)(0x68046162)()
F (../Core/Src/stm32f4xx_it.c)(0x68034E5D)()
F (../Core/Src/stm32f4xx_hal_msp.c)(0x67F5EAC2)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x67D552C4)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x67D552C5)()
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x67D552C5)()
F (../Core/Src/system_stm32f4xx.c)(0x67EBF10E)()
F (..\Components\interface\src\delay.c)(0x674C3DEA)()
F (..\Components\interface\src\iic.c)(0x674C3DEA)()
F (..\Components\interface\src\wire.c)(0x67F624E6)()
F (..\Components\ssd1106\src\driver_ssd1306.c)(0x674C3DEA)()
F (..\Components\ssd1106\interface\stm32f407_driver_ssd1306_interface.c)(0x67F624FD)()
F (..\Components\ringbuffer\ringbuffer.c)(0x67FB299A)()
F (..\Components\multi_timer\MultiTimer.c)(0x67FA71C5)()
F (..\Components\motor\Emm_V5.c)(0x68045E6A)()
F (..\Components\pid\pid.c)(0x68131EF5)()
F (..\App\mydefine.h)(0x680B4449)()
F (..\App\app_oled.c)(0x67FA8DB8)()
F (..\App\app_motor.c)(0x67FB2760)()
F (..\App\app_uasrt.c)(0x6810DFA0)()
F (..\App\app_maixcam.c)(0x6813248E)()
F (..\App\app_pid.c)(0x6813248E)()
